import { BrainIcon } from "../../icons/BrainIcon"
import { FocusIcon } from "../../icons/FocusIcon"
import { OutlineIcon } from "../../icons/OutlineIcon"
import { PositionIcon } from "../../icons/PositionIcon"
import { ViewIcon } from "../../icons/ViewIcon"
import {
  SearchIcon
} from "../icons"
import type { ToolbarItem } from "./startToolbarConfig"

// 视图工具栏配置
export const viewToolbarConfig: ToolbarItem[] = [
  // 节点样式组
  {
    type: "button",
    id: "brain",
    label: "脑图模式",
    icon: BrainIcon,
    text: "脑图模式",
    className: "toolbar-text-btn",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "outline",
    label: "大纲模式",
    icon: OutlineIcon,
    text: "大纲模式",
    disabled: "!isEnabled",
  },
  { type: "separator" },
  {
    type: "button",
    id: "focus",
    label: "聚焦模式",
    icon: FocusIcon,
    text: "聚焦模式",
    disabled: "!isEnabled",
  },

  {
    type: "button",
    id: "position",
    label: "定位到中心主题",
    icon: PositionIcon,
    text: "定位到中心主题",
    disabled: "!isEnabled",
  },
  { type: "separator" },
  {
    type: "button",
    id: "searcch",
    label: "查找替换",
    icon: SearchIcon,
    text: "查找替换",
    disabled: "!isEnabled",
  },
  {
    type: "button",
    id: "view",
    label: "视图导航",
    icon: ViewIcon,
    text: "视图导航",
    disabled: "!isEnabled",
  },

]
