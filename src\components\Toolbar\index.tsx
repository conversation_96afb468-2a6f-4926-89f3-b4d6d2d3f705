import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"

import { ExportToolbar } from "./sections/ExportToolbar"
import { InsertToolbar } from "./sections/InsertToolbar"
import { StartToolbar } from "./sections/StartToolbar"
import { StyleToolbar } from "./sections/StyleToolbar"
import { ViewToolbar } from "./sections/ViewToolbar"
import type { ToolbarProps } from "./type/types"

const Toolbar: React.FC<ToolbarProps> = (props) => {
  const {
    activeTab,
    selectedNodeId,
    selectedNodeStyle,
    onAddChildNode,
    onToggleBold,
    onToggleItalic,
    onColorChange,
    onFontFamilyChange,
    onFontSizeChange,
    onTextAlignChange,
    onBorderWidthChange,
  } = props

  const sectionProps = {
    selectedNodeId,
    selectedNodeStyle,
    onAddChildNode,
    onToggleBold,
    onToggleItalic,
    onColorChange,
    onFontFamilyChange,
    onFontSizeChange,
    onTextAlignChange,
    onBorderWidthChange,
  }

  const renderToolbar = () => {
    switch (activeTab) {
      case "开始":
        return <StartToolbar {...sectionProps} />
      case "样式":
        return <StyleToolbar {...sectionProps} />
      case "导出":
        return <ExportToolbar />
      case "插入":
        return <InsertToolbar {...sectionProps} />
      case "视图":
        return <ViewToolbar {...sectionProps} />
      default:
        return <div className="toolbar">空</div>
    }
  }

  return (
    <Tooltip.Provider delayDuration={100}>{renderToolbar()}</Tooltip.Provider>
  )
}

export default Toolbar
