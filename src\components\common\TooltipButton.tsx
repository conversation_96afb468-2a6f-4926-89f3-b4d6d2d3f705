import * as Toolbar from "@radix-ui/react-toolbar"
import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"

interface TooltipButtonProps {
  label: string
  onClick?: () => void
  disabled?: boolean
  active?: boolean
  className?: string
  children: React.ReactNode
}

export const TooltipButton: React.FC<TooltipButtonProps> = ({
  label,
  onClick,
  disabled,
  active,
  className = "",
  children,
}) => {
  return (
    <Tooltip.Provider delayDuration={200}>
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <Toolbar.Button
            type="button"
            onClick={(e) => {
              e.stopPropagation()
              if (!disabled && onClick) onClick()
            }}
            disabled={disabled}
            data-state={active ? "on" : "off"}
            className={`toolbar-icon-btn ${active ? "active" : ""} ${
              disabled ? "disabled" : ""
            } ${className}`}
          >
            {children}
          </Toolbar.Button>
        </Tooltip.Trigger>
        <Tooltip.Content className="tooltip-content" side="bottom" align="center">
          {label}
          <Tooltip.Arrow className="tooltip-arrow" />
        </Tooltip.Content>
      </Tooltip.Root>
    </Tooltip.Provider>
  )
}
