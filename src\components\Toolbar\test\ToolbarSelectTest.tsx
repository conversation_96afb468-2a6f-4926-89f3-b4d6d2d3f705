import React, { useState } from "react"
import { ToolbarSelect } from "../../common/ToolbarSelect"

export const ToolbarSelectTest: React.FC = () => {
  const [fontSize, setFontSize] = useState<number>(14)
  const [fontFamily, setFontFamily] = useState<string>("Arial")

  return (
    <div className="p-4 space-y-4">
      <h3>ToolbarSelect 测试</h3>
      
      <div>
        <h4>字号选择 (当前值: {fontSize})</h4>
        <ToolbarSelect
          label="字号"
          value={fontSize}
          options={["12", "14", "16", "18", "20", "24", "28", "32"]}
          onChange={(value) => {
            console.log("字号改变:", value, typeof value)
            setFontSize(parseInt(value, 10))
          }}
          className="toolbar-size-select"
        />
      </div>

      <div>
        <h4>字体选择 (当前值: {fontFamily})</h4>
        <ToolbarSelect
          label="字体"
          value={fontFamily}
          options={["Arial", "Helvetica", "Times New Roman", "Courier New"]}
          onChange={(value) => {
            console.log("字体改变:", value, typeof value)
            setFontFamily(value)
          }}
          className="toolbar-font-select"
        />
      </div>

      <div>
        <h4>测试结果</h4>
        <p>字号: {fontSize} (类型: {typeof fontSize})</p>
        <p>字体: {fontFamily} (类型: {typeof fontFamily})</p>
      </div>
    </div>
  )
}
